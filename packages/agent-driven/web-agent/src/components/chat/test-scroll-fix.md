# 滚动问题修复测试

## 问题描述
当 CodeAccordianAdaptor 在聊天框最下面时，点击展开后整个聊天框会滚动到中间，再折叠后消息不再在最下面。

## 修复内容
优化了 `toggleRowExpansion` 方法中的滚动逻辑：

### 原始逻辑问题
1. 展开最后一个或倒数第二个消息时，总是会调用 `scrollToIndex` 滚动到特定位置
2. 折叠时，如果用户在底部，会滚动到底部
3. 这些滚动操作导致消息位置发生不必要的变化

### 修复后的逻辑
1. **折叠时**：只有当用户在底部且是最后一个消息时才滚动到底部
2. **展开时**：只有当展开的内容可能超出视窗且用户不在底部时才调整滚动位置
3. 避免了不必要的滚动，特别是当消息已经在底部时

## 测试步骤
1. 在聊天框中创建一个新文件的消息，确保它在最下面
2. 点击 CodeAccordianAdaptor 展开代码
3. 验证聊天框没有滚动到中间
4. 再次点击折叠代码
5. 验证消息仍然在最下面

## 预期结果
- 展开/折叠操作不会导致不必要的滚动
- 当消息在底部时，展开/折叠后仍保持在底部
- 只有在必要时（内容超出视窗且用户不在底部）才进行滚动调整
