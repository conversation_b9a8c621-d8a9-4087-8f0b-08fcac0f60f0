import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';
import debounce from 'debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDeepCompareEffect, useEvent, useMount } from 'react-use';
import { Virtuoso, type VirtuosoHandle } from 'react-virtuoso';
import styled from 'styled-components';
import {
  JoyCoderApiReqInfo,
  JoyCoderAsk,
  JoyCoderMessage,
  JoyCoderSayBrowserAction,
  JoyCoderSayTool,
  ExtensionMessage,
} from '../../../../src/shared/ExtensionMessage';
import { findLast } from '../../../../src/shared/array';
import { combineApiRequests } from '../../../../src/shared/combineApiRequests';
import { combineCommandSequences } from '../../../../src/shared/combineCommandSequences';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { vscode } from '../../utils/vscode';
import { getModeBySlug, modes } from '../../utils/modes';
// import HistoryPreview from '../history/HistoryPreview';
import { normalizeApiConfiguration } from '../settings/ApiOptions';
import BrowserSessionRow from './BrowserSessionRow';
import ChatRow from './ChatRow';
import ChatTextArea from './ChatTextArea';
import ChatHistoryView from '../history/ChatHistoryView';
import ParentTaskHeader from '../../adaptor/components/parentTask';
import { useHistoryPrompt } from '../../utils/useHistoryPrompt';
import '../../adaptor/common.scss';
// import AutoApproveMenu from './AutoApproveMenu';
import McpView from '../../components/mcp/McpView';
import Index from '../../adaptor';
import { locale } from '../../adaptor/locales';
import { delContextItem, getContextItemContents, setupContextItemCloseHandlers } from '../../utils/context-mentions';
import Login from '../../adaptor/login/login';

interface ChatViewProps {
  isHidden: boolean;
  showAnnouncement: boolean;
  hideAnnouncement: () => void;
  showHistoryView: () => void;
  showSettingView: () => void;
  setShowMcpView: () => void;
  showChatView: () => void;
  setShowPromptView: () => void;
}

export const MAX_IMAGES_PER_MESSAGE = 20; // Anthropic limits to 20 images
export const DEFAULT_ASSISTANT_AVATAR =
  'https://img30.360buyimg.com/img/jfs/t1/222950/2/30082/4115/6521193cFc19f1365/b0070eb19e1f2566.png';
export interface ChatModelConfig {
  label: string;
  description: string;
  avatar: string;
  chatApiModel: string;
  chatApiUrl?: string;
  maxTotalTokens: number;
  bizId?: string;
  bizToken?: string;
  systemMessage?: string;
  respMaxTokens?: number;
  // 标记类
  hidden?: boolean;
  prefer?: boolean;
  context?: boolean;
  features?: string[];
  temperature?: number;
  stream?: boolean;
}

interface parentTaskInfo {
  rootTask: any;
  isParentTask: boolean;
}
// const newChat = () => {
//   vscode.postMessage({ type: 'openInNewTab', text: '', images: [''] });
// };
const ChatView = ({ isHidden, showChatView, setShowMcpView, showHistoryView, setShowPromptView }: ChatViewProps) => {
  const {
    joycoderMessages: messages,
    apiConfiguration,
    chatSettings,
    mode: currentModeSlug,
    customModes,
  } = useExtensionState();

  // Get complete mode information including custom modes
  // Use a stable reference for customModes to prevent unnecessary re-renders
  const stableCustomModes = useRef(customModes);
  const customModesChanged = useRef(false);

  // Only update the stable reference when content actually changes
  if (JSON.stringify(stableCustomModes.current) !== JSON.stringify(customModes)) {
    stableCustomModes.current = customModes;
    customModesChanged.current = !customModesChanged.current; // Toggle to trigger useMemo
  }

  const currentMode = useMemo(() => {
    let mode;
    if (currentModeSlug) {
      mode = getModeBySlug(currentModeSlug, stableCustomModes.current) || modes[0];
    } else {
      mode = modes[0]; // fallback to first mode (chat)
    }

    // Ensure the mode has proper name and agentId properties
    return {
      ...mode,
      // Ensure name exists for fallback
      name: mode.name || '智能体',
      // Ensure agentId exists
      agentId: mode.agentId || 'chat',
    };
  }, [currentModeSlug]);

  // const [showHistoryPreview, setShowHistoryPreview] = useState(false);
  const [showMCP, setShowMCP] = useState(false);
  const [isIDE, setIsIDE] = useState(false);
  const [isLogined, setIsLogined] = useState<boolean | undefined>(undefined);
  const [isRemoteEnvironment, setIsRemoteEnvironment] = useState(false);

  const checkLogin = () => {
    vscode.postMessage({ type: 'CHECK_LOGIN_STATUS' });
    vscode.postMessage({ type: 'CHECK_PLUGIN_TYPE' });
  };
  // const toggleHistoryPreview = () => {
  //   setShowHistoryPreview(!showHistoryPreview);
  // };
  const { handleHistoryPrompt, setHistoryPrompt } = useHistoryPrompt({
    onChoosePrompt: (prompt) => {
      delContextItem();
      setInputValue(prompt);
    },
  });
  //const task = messages.length > 0 ? (messages[0].say === "task" ? messages[0] : undefined) : undefined) : undefined
  const task = useMemo(() => messages.at(0), [messages]); // leaving this less safe version here since if the first message is not a task, then the extension is in a bad state and needs to be debugged (see JoyCoder.abort)
  const modifiedMessages = useMemo(() => combineApiRequests(combineCommandSequences(messages.slice(1))), [messages]);

  const [inputValue, setInputValue] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [textAreaDisabled, setTextAreaDisabled] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // we need to hold on to the ask because useEffect > lastMessage will always let us know when an ask comes in and handle it, but by the time handleMessage is called, the last message might not be the ask anymore (it could be a say that followed)
  const [joycoderAsk, setJoyCoderAsk] = useState<JoyCoderAsk | undefined>(undefined);
  const [enableButtons, setEnableButtons] = useState<boolean>(false);
  const [primaryButtonText, setPrimaryButtonText] = useState<string | undefined>(undefined);
  const [secondaryButtonText, setSecondaryButtonText] = useState<string | undefined>(undefined);
  // const [buttonText, setButtonText] = useState<ButtonText | undefined>({
  //   primaryButtonText: undefined,
  //   secondaryButtonText: undefined,
  // });
  const [didClickCancel, setDidClickCancel] = useState(false);
  const virtuosoRef = useRef<VirtuosoHandle>(null);
  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({});
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const disableAutoScrollRef = useRef(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isAtBottom, setIsAtBottom] = useState(false);
  const [isMacOs, setIsMacOs] = useState(false);
  const [postTaskInfo, setPostTaskInfo] = useState<parentTaskInfo>();

  // UI layout depends on the last 2 messages
  // (since it relies on the content of these messages, we are deep comparing. i.e. the button state after hitting button sets enableButtons to false, and this effect otherwise would have to true again even if messages didn't change
  const lastMessage = useMemo(() => messages.at(-1), [messages]);
  const secondLastMessage = useMemo(() => messages.at(-2), [messages]);
  useDeepCompareEffect(() => {
    // if last message is an ask, show user ask UI
    // if user finished a task, then start a new task with a new conversation history since in this moment that the extension is waiting for user response, the user could close the extension and the conversation history would be lost.
    // basically as long as a task is active, the conversation history will be persisted
    if (lastMessage) {
      switch (lastMessage.type) {
        case 'ask':
          const isPartial = lastMessage.partial === true;
          switch (lastMessage.ask) {
            case 'api_req_failed':
              setTextAreaDisabled(true);
              setJoyCoderAsk('api_req_failed');
              setEnableButtons(true);
              setPrimaryButtonText(locale.buttons.primary.retry);
              setSecondaryButtonText(locale.buttons.secondary.startNew);
              break;
            case 'mistake_limit_reached':
              setTextAreaDisabled(false);
              setJoyCoderAsk('mistake_limit_reached');
              setEnableButtons(true);
              setPrimaryButtonText(locale.buttons.primary.proceed);
              setSecondaryButtonText(locale.buttons.secondary.startNew);
              break;
            case 'auto_approval_max_req_reached':
              setTextAreaDisabled(true);
              setJoyCoderAsk('auto_approval_max_req_reached');
              setEnableButtons(true);
              setPrimaryButtonText(locale.buttons.primary.proceed);
              setSecondaryButtonText(locale.buttons.secondary.startNew);
              break;
            case 'followup':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('followup');
              setEnableButtons(isPartial);
              // setPrimaryButtonText(undefined)
              // setSecondaryButtonText(undefined)
              break;
            case 'ask_followup_question':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('ask_followup_question');
              setEnableButtons(false);
              // setPrimaryButtonText(undefined)
              // setSecondaryButtonText(undefined)
              break;
            case 'tool':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('tool');
              setEnableButtons(!isPartial);
              const tool = JSON.parse(lastMessage.text || '{}') as JoyCoderSayTool;
              switch (tool.tool) {
                case 'editedExistingFile':
                case 'appliedDiff':
                case 'newFileCreated':
                case 'insertContent':
                  setPrimaryButtonText(locale.buttons.primary.editFile);
                  setSecondaryButtonText(locale.buttons.secondary.editFile);
                  break;
                case 'finishTask':
                  setPrimaryButtonText('完成子任务并返回');
                  setSecondaryButtonText(undefined);
                  break;
                default:
                  setPrimaryButtonText(locale.buttons.primary.default);
                  setSecondaryButtonText(locale.buttons.secondary.default);
                  break;
              }
              break;
            case 'use_browser_launch':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('use_browser_launch');
              setEnableButtons(!isPartial);
              setPrimaryButtonText(locale.buttons.primary.default);
              setSecondaryButtonText(locale.buttons.secondary.default);
              // setButtonText({
              //   primaryButtonText: locale.buttons.primary.default,
              //   secondaryButtonText: locale.buttons.secondary.default,
              // });
              break;
            case 'command':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('command');
              setEnableButtons(!isPartial);
              setPrimaryButtonText(locale.buttons.primary.command);
              setSecondaryButtonText(locale.buttons.secondary.default);
              break;
            case 'command_output':
              setTextAreaDisabled(false);
              setJoyCoderAsk('command_output');
              setEnableButtons(true);
              setPrimaryButtonText(locale.common.proceedWhileRunning);
              setSecondaryButtonText(undefined);
              break;
            case 'use_mcp_server':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('use_mcp_server');
              setEnableButtons(!isPartial);
              setPrimaryButtonText(locale.buttons.primary.default);
              setSecondaryButtonText(locale.buttons.secondary.reject);
              break;
            case 'completion_result':
              // extension waiting for feedback. but we can just present a new task button
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('completion_result');
              setEnableButtons(!isPartial);
              setPrimaryButtonText(locale.buttons.primary.startNew);
              setSecondaryButtonText(undefined);
              break;
            case 'resume_task':
              setTextAreaDisabled(false);
              setJoyCoderAsk('resume_task');
              setEnableButtons(true);
              setPrimaryButtonText(locale.buttons.primary.resume);
              setSecondaryButtonText(undefined);
              setDidClickCancel(false); // special case where we reset the cancel button state
              break;
            case 'resume_completed_task':
              setTextAreaDisabled(false);
              setJoyCoderAsk('resume_completed_task');
              setEnableButtons(true);
              setPrimaryButtonText(locale.buttons.primary.startNew);
              setSecondaryButtonText(undefined);
              setDidClickCancel(false);
              break;
            case 'new_task_with_condense_context':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('new_task_with_condense_context');
              setEnableButtons(!isPartial);
              setPrimaryButtonText('创建带上下文的新任务');
              setSecondaryButtonText(undefined);
              break;
            case 'condense':
              setTextAreaDisabled(isPartial);
              setJoyCoderAsk('condense');
              setEnableButtons(!isPartial);
              setPrimaryButtonText('压缩上下文并继续');
              setSecondaryButtonText(undefined);
              break;
          }
          break;
        case 'say':
          // don't want to reset since there could be a "say" after an "ask" while ask is waiting for response
          switch (lastMessage.say) {
            case 'api_req_started':
              if (secondLastMessage?.ask === 'command_output') {
                // if the last ask is a command_output, and we receive an api_req_started, then that means the command has finished and we don't need input from the user anymore (in every other case, the user has to interact with input field or buttons to continue, which does the following automatically)
                setInputValue('');
                setTextAreaDisabled(true);
                setSelectedImages([]);
                setJoyCoderAsk(undefined);
                setEnableButtons(false);
              }
              break;
            case 'task':
            case 'error':
            case 'api_req_finished':
            case 'text':
            case 'use_browser':
            case 'use_browser_result':
            case 'use_browser_launch':
            case 'command':
            case 'use_mcp_server':
            case 'command_output':
            case 'mcp_server_request_started':
            case 'mcp_server_response':
            case 'completion_result':
            case 'tool':
            case 'get_mcp_instructions':
              break;
          }
          break;
      }
    } else {
      // this would get called after sending the first message, so we have to watch messages.length instead
      // No messages, so user has to submit a task
      // setTextAreaDisabled(false)
      // setJoyCoderAsk(undefined)
      // setPrimaryButtonText(undefined)
      // setSecondaryButtonText(undefined)
    }
  }, [lastMessage, secondLastMessage]);

  useEffect(() => {
    if (messages.length === 0) {
      setTextAreaDisabled(false);
      setJoyCoderAsk(undefined);
      setEnableButtons(false);
      setPrimaryButtonText(undefined);
      setSecondaryButtonText(undefined);
    }
  }, [messages.length]);

  useEffect(() => {
    document.addEventListener('click', (event: MouseEvent) => {
      const target = event.target;
      if (target instanceof Element && target.closest('.joycoder-login-btn')) {
        vscode.postMessage({ type: 'JUMP_LOGIN' });
      }
    });
    vscode.postMessage({ type: 'chat-get-system' });
    setupContextItemCloseHandlers();
  }, []);

  useEffect(() => {
    if (!isLogined) {
      checkLogin();
    }
  }, [isLogined]);

  useEffect(() => {
    setExpandedRows({});
  }, [task?.ts]);

  const isStreaming = useMemo(() => {
    const isLastAsk = !!modifiedMessages.at(-1)?.ask; // checking joycoderAsk isn't enough since messages effect may be called again for a tool for example, set joycoderAsk to its value, and if the next message is not an ask then it doesn't reset. This is likely due to how much more often we're updating messages as compared to before, and should be resolved with optimizations as it's likely a rendering bug. but as a final guard for now, the cancel button will show if the last message is not an ask
    const isToolCurrentlyAsking =
      isLastAsk && joycoderAsk !== undefined && enableButtons && primaryButtonText !== undefined;
    if (isToolCurrentlyAsking) {
      return false;
    }

    const isLastMessagePartial = modifiedMessages.at(-1)?.partial === true;
    if (isLastMessagePartial) {
      return true;
    } else {
      const lastApiReqStarted = findLast(modifiedMessages, (message) => message.say === 'api_req_started');
      if (lastApiReqStarted && lastApiReqStarted.text != null && lastApiReqStarted.say === 'api_req_started') {
        const cost = JSON.parse(lastApiReqStarted.text).cost;
        if (cost === undefined) {
          // api request has not finished yet
          return true;
        }
      }
    }

    return false;
  }, [modifiedMessages, joycoderAsk, enableButtons, primaryButtonText]);

  const handleSendMessage = useCallback(
    (text: string, images: string[]) => {
      const contextContents = getContextItemContents(); // 添加选中代码的文件
      text = text.trim() + contextContents;

      setHistoryPrompt(text);
      if (text || images.length > 0) {
        if (messages.length === 0) {
          vscode.postMessage({ type: 'newTask', text, images });
        } else if (joycoderAsk) {
          switch (joycoderAsk) {
            case 'followup':
            case 'ask_followup_question':
            case 'tool':
            case 'use_browser_launch':
            case 'command': // user can provide feedback to a tool or command use
            case 'command_output': // user can send input to command stdin
            case 'use_mcp_server':
            case 'completion_result': // if this happens then the user has feedback for the completion result
            case 'resume_task':
            case 'resume_completed_task':
            case 'mistake_limit_reached':
            case 'new_task_with_condense_context': // user can provide feedback or reject the new task suggestion
            case 'condense':
              vscode.postMessage({
                type: 'askResponse',
                askResponse: 'messageResponse',
                text,
                images,
              });
              break;
            // there is no other case that a textfield should be enabled
          }
        }
        setInputValue('');
        setTextAreaDisabled(true);
        setSelectedImages([]);
        setJoyCoderAsk(undefined);
        setEnableButtons(false);
        // setPrimaryButtonText(undefined)
        // setSecondaryButtonText(undefined)
        disableAutoScrollRef.current = false;
      }
    },
    [
      messages.length,
      joycoderAsk,
      setInputValue,
      setTextAreaDisabled,
      setSelectedImages,
      setJoyCoderAsk,
      setEnableButtons,
      setHistoryPrompt,
    ],
  );

  const startNewTask = useCallback(() => {
    vscode.postMessage({ type: 'clearTask' });
  }, []);

  /*
	This logic depends on the useEffect[messages] above to set joycoderAsk, after which buttons are shown and we then send an askResponse to the extension.
	*/
  const handlePrimaryButtonClick = useCallback(
    (text?: string, images?: string[]) => {
      const trimmedInput = !!text && typeof text === 'string' ? text?.trim() : '';
      switch (joycoderAsk) {
        case 'api_req_failed':
        case 'command':
        case 'command_output':
        case 'tool':
        case 'use_browser_launch':
        case 'use_mcp_server':
        case 'resume_task':
        case 'mistake_limit_reached':
        case 'auto_approval_max_req_reached':
          if (trimmedInput || (images && images.length > 0)) {
            vscode.postMessage({
              type: 'askResponse',
              askResponse: 'yesButtonClicked',
              text: trimmedInput,
              images: images,
            });
          } else {
            vscode.postMessage({
              type: 'askResponse',
              askResponse: 'yesButtonClicked',
            });
          }
          // Clear input state after sending
          setInputValue('');
          setSelectedImages([]);
          break;
        case 'completion_result':
        case 'resume_completed_task':
          // extension waiting for feedback. but we can just present a new task button
          startNewTask();
          break;
        case 'new_task_with_condense_context':
          vscode.postMessage({
            type: 'newTask',
            text: lastMessage?.text,
          });
          break;
        case 'condense':
          vscode.postMessage({
            type: 'condense',
            text: lastMessage?.text,
          });
          break;
      }
      setTextAreaDisabled(true);
      setJoyCoderAsk(undefined);
      setEnableButtons(false);
      // setPrimaryButtonText(undefined)
      // setSecondaryButtonText(undefined)
      disableAutoScrollRef.current = false;
    },
    [joycoderAsk, lastMessage?.text, startNewTask],
  );

  const handleSecondaryButtonClick = useCallback(
    (text?: string, images?: string[], isTerminate: boolean = false) => {
      const trimmedInput = !!text && typeof text === 'string' ? text?.trim() : '';
      if (isStreaming || isTerminate) {
        vscode.postMessage({ type: 'cancelTask' });
        setDidClickCancel(true);
        return;
      }
      switch (joycoderAsk) {
        case 'api_req_failed':
        case 'mistake_limit_reached':
        case 'resume_task':
        case 'auto_approval_max_req_reached':
          startNewTask();
          break;
        case 'command':
        case 'tool':
        case 'use_browser_launch':
        case 'use_mcp_server':
          if (trimmedInput || (images && images.length > 0)) {
            vscode.postMessage({
              type: 'askResponse',
              askResponse: 'noButtonClicked',
              text: trimmedInput,
              images: images,
            });
          } else {
            // responds to the API with a "This operation failed" and lets it try again
            vscode.postMessage({
              type: 'askResponse',
              askResponse: 'noButtonClicked',
            });
          }
          // Clear input state after sending
          setInputValue('');
          setSelectedImages([]);
          break;
        case 'command_output':
          vscode.postMessage({ type: 'terminalOperation', terminalOperation: 'abort' });
          break;
        // responds to the API with a "This operation failed" and lets it try again
        // vscode.postMessage({ type: 'askResponse', askResponse: 'noButtonClicked' });
        // break;
      }

      setTextAreaDisabled(true);
      setJoyCoderAsk(undefined);
      setEnableButtons(false);
      // setPrimaryButtonText(undefined)
      // setSecondaryButtonText(undefined)
      disableAutoScrollRef.current = false;
    },
    [joycoderAsk, startNewTask, isStreaming],
  );

  const { selectedModelInfo } = useMemo(() => {
    return normalizeApiConfiguration(apiConfiguration);
  }, [apiConfiguration]);

  const selectImages = useCallback(() => {
    vscode.postMessage({ type: 'selectImages' });
  }, []);

  const shouldDisableImages =
    !selectedModelInfo.supportsImages || textAreaDisabled || selectedImages.length >= MAX_IMAGES_PER_MESSAGE;

  const handleMessage = useCallback(
    (e: MessageEvent) => {
      const message: ExtensionMessage = e.data;
      switch (message.type) {
        case 'action':
          switch (message.action!) {
            case 'didBecomeVisible':
              if (!isHidden && !textAreaDisabled && !enableButtons) {
                textAreaRef.current?.focus();
              }
              break;
            case 'mcpButtonClicked':
              // toggleMCP();
              setShowMCP(true);
              setShowMcpView();
              setIsRemoteEnvironment(message.data?.isRemoteEnvironment);
              break;
            case 'historyButtonClicked':
              showHistoryView && showHistoryView();
              setShowMCP(false);
              break;
            case 'openInNewChat':
              setShowMCP(false);
              showChatView();
              break;
            case 'chatButtonClicked':
              setShowMCP(false);
              showChatView();
              break;
          }
          break;
        case 'selectedImages':
          const newImages = message.images ?? [];
          if (newImages.length > 0) {
            setSelectedImages((prevImages) => [...prevImages, ...newImages].slice(0, MAX_IMAGES_PER_MESSAGE));
          }
          break;
        case 'updatePlatformInfo':
          setIsMacOs(!!message?.data?.isMac);
          break;
        case 'updatePluginType':
          setIsIDE(!!message?.data?.isIde);
          break;
        case 'updateLoginStatus':
          setIsLogined(!!message?.data?.isLogin);
          break;
        case 'postTaskInfo':
          setPostTaskInfo(message?.parentTaskInfo);
          break;
        case 'noWorkspaceError':
          // 处理没有工作空间的错误消息
          // 这里可以显示一个特殊的消息或者触发特定的UI状态
          // 暂时通过添加一个错误消息来处理
          if (message.text) {
            // 可以在这里添加特殊的处理逻辑，比如显示一个模态框或者特殊的提示
            console.warn('No workspace error:', message.text);
          }
          break;
        case 'invoke': {
          switch (message.invoke!) {
            case 'sendMessage':
              handleSendMessage(message.text ?? '', message.images ?? []);
              break;
            case 'primaryButtonClick':
              handlePrimaryButtonClick(message.text ?? '', message.images ?? []);
              break;
            case 'secondaryButtonClick':
              handleSecondaryButtonClick(message.text ?? '', message.images ?? []);
              break;
          }
          break;
        }
      }
      // textAreaRef.current is not explicitly required here since react gaurantees that ref will be stable across re-renders, and we're not using its value but its reference.
    },
    [
      isHidden,
      textAreaDisabled,
      enableButtons,
      setShowMcpView,
      showHistoryView,
      showChatView,
      handleSendMessage,
      handlePrimaryButtonClick,
      handleSecondaryButtonClick,
    ],
  );

  useEvent('message', handleMessage);

  useMount(() => {
    // NOTE: the vscode window needs to be focused for this to work
    textAreaRef.current?.focus();
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isHidden && !textAreaDisabled && !enableButtons) {
        textAreaRef.current?.focus();
      }
    }, 50);
    return () => {
      clearTimeout(timer);
    };
  }, [isHidden, textAreaDisabled, enableButtons]);

  const isMessageTextEmpty = (message: JoyCoderMessage) => {
    try {
      return (JSON.parse(message.text || '{}').text ?? '').trim() === '';
    } catch (e) {
      return message.text === '';
    }
  };

  const visibleMessages = useMemo(() => {
    return modifiedMessages.filter((message) => {
      switch (message.ask) {
        case 'completion_result': {
          // don't show a chat row for a completion_result ask without text. This specific type of message only occurs if joycoder wants to execute a command as part of its completion result, in which case we interject the completion_result tool with the use_command tool.
          return !isMessageTextEmpty(message);
        }
        case 'api_req_failed': // this message is used to update the latest api_req_started that the request failed
        case 'resume_task':
        case 'resume_completed_task':
          return false;
      }
      switch (message.say) {
        case 'api_req_started': {
          // 隐藏checkpoint_created、api_req_started，在自动化编程对话中不需要显示步骤标题
          if (message.text != null) {
            const info: JoyCoderApiReqInfo = JSON.parse(message.text);
            if (info.cost != null && info.cancelReason == null) return false;
          }
          break;
        }
        case 'api_req_finished': // combineApiRequests removes this from modifiedMessages anyways
        case 'api_req_retried': // this message is used to update the latest api_req_started that the request was retried
        case 'deleted_api_reqs': // aggregated api_req metrics from deleted messages
          return false;
        case 'text':
          // Sometimes joycoder returns an empty text message, we don't want to render these. (We also use a say text for user messages, so in case they just sent images we still render that)
          if (isMessageTextEmpty(message) && (message.images?.length ?? 0) === 0) {
            return false;
          }
          break;
        case 'mcp_server_request_started':
          return false;
      }
      return true;
    });
  }, [modifiedMessages]);

  const isBrowserSessionMessage = (message: JoyCoderMessage): boolean => {
    // which of visible messages are browser session messages, see above
    if (message.type === 'ask') {
      return ['use_browser_launch'].includes(message.ask!);
    }
    if (message.type === 'say') {
      return ['use_browser_launch', 'api_req_started', 'text', 'use_browser', 'use_browser_result'].includes(
        message.say!,
      );
    }
    return false;
  };

  const groupedMessages = useMemo(() => {
    // console.log('visibleMessages::::', visibleMessages); //调试查看全部可见消息
    const result: (JoyCoderMessage | JoyCoderMessage[])[] = [];
    let currentGroup: JoyCoderMessage[] = [];
    let isInBrowserSession = false;

    const endBrowserSession = () => {
      if (currentGroup.length > 0) {
        result.push([...currentGroup]);
        currentGroup = [];
        isInBrowserSession = false;
      }
    };

    // 将任务的初始文本转换为第一条用户消息
    const messagesToProcess = task
      ? [
          {
            type: 'say' as const,
            say: 'user_feedback' as const,
            text: task.text,
            ts: task.ts - 1, // Ensure it appears before other messages
            images: task.images,
          } as JoyCoderMessage,
          ...visibleMessages,
        ]
      : visibleMessages;

    messagesToProcess.forEach((message) => {
      if (message.ask === 'use_browser_launch' || message.say === 'use_browser_launch') {
        // complete existing browser session if any
        endBrowserSession();
        // start new
        isInBrowserSession = true;
        currentGroup.push(message);
      } else if (isInBrowserSession) {
        // end session if api_req_started is cancelled

        if (message.say === 'api_req_started') {
          // get last api_req_started in currentGroup to check if it's cancelled. If it is then this api req is not part of the current browser session
          const lastApiReqStarted = [...currentGroup].reverse().find((m) => m.say === 'api_req_started');
          if (lastApiReqStarted?.text != null) {
            const info = JSON.parse(lastApiReqStarted.text);
            const isCancelled = info.cancelReason != null;
            if (isCancelled) {
              endBrowserSession();
              result.push(message);
              return;
            }
          }
        }

        if (isBrowserSessionMessage(message)) {
          currentGroup.push(message);

          // Check if this is a close action
          if (message.say === 'use_browser') {
            const browserAction = JSON.parse(message.text || '{}') as JoyCoderSayBrowserAction;
            if (browserAction.action === 'close') {
              endBrowserSession();
            }
          }
        } else {
          // complete existing browser session if any
          endBrowserSession();
          result.push(message);
        }
      } else {
        result.push(message);
      }
    });

    // Handle case where browser session is the last group
    if (currentGroup.length > 0) {
      result.push([...currentGroup]);
    }

    return result;
  }, [visibleMessages, task]);

  // scrolling

  const scrollToBottomSmooth = useMemo(
    () =>
      debounce(
        () => {
          virtuosoRef.current?.scrollTo({
            top: Number.MAX_SAFE_INTEGER,
            behavior: 'smooth',
          });
        },
        10,
        { immediate: true },
      ),
    [],
  );

  const scrollToBottomAuto = useCallback(() => {
    virtuosoRef.current?.scrollTo({
      top: Number.MAX_SAFE_INTEGER,
      behavior: 'auto', // instant causes crash
    });
  }, []);

  // scroll when user toggles certain rows
  const toggleRowExpansion = useCallback(
    (ts: number) => {
      const isCollapsing = expandedRows[ts] ?? false;
      const lastGroup = groupedMessages.at(-1);
      const isLast = Array.isArray(lastGroup) ? lastGroup[0].ts === ts : lastGroup?.ts === ts;
      const secondToLastGroup = groupedMessages.at(-2);
      const isSecondToLast = Array.isArray(secondToLastGroup)
        ? secondToLastGroup[0].ts === ts
        : secondToLastGroup?.ts === ts;

      setExpandedRows((prev) => ({
        ...prev,
        [ts]: !prev[ts],
      }));

      // disable auto scroll when user expands row
      if (!isCollapsing) {
        disableAutoScrollRef.current = true;
      }

      // 优化滚动逻辑：只在必要时进行滚动
      if (isCollapsing) {
        // 折叠时：只有当用户在底部且是最后一个消息时才滚动到底部
        if (isAtBottom && isLast) {
          const timer = setTimeout(() => {
            scrollToBottomAuto();
          }, 0);
          return () => clearTimeout(timer);
        }
      } else {
        // 展开时：只有当展开的内容可能超出视窗时才调整滚动位置
        // 避免不必要的滚动，特别是当消息已经在底部时
        if ((isLast || isSecondToLast) && !isAtBottom) {
          const timer = setTimeout(() => {
            virtuosoRef.current?.scrollToIndex({
              index: groupedMessages.length - (isLast ? 1 : 2),
              align: 'start',
            });
          }, 0);
          return () => clearTimeout(timer);
        }
      }
    },
    [groupedMessages, expandedRows, scrollToBottomAuto, isAtBottom],
  );

  const handleRowHeightChange = useCallback(
    (isTaller: boolean) => {
      if (!disableAutoScrollRef.current) {
        if (isTaller) {
          scrollToBottomSmooth();
        } else {
          setTimeout(() => {
            scrollToBottomAuto();
          }, 0);
        }
      }
    },
    [scrollToBottomSmooth, scrollToBottomAuto],
  );

  useEffect(() => {
    if (!disableAutoScrollRef.current) {
      setTimeout(() => {
        scrollToBottomSmooth();
      }, 50);
      // return () => clearTimeout(timer) // dont cleanup since if visibleMessages.length changes it cancels.
    }
  }, [groupedMessages.length, scrollToBottomSmooth]);

  const handleWheel = useCallback((event: Event) => {
    const wheelEvent = event as WheelEvent;
    if (wheelEvent.deltaY && wheelEvent.deltaY < 0) {
      if (scrollContainerRef.current?.contains(wheelEvent.target as Node)) {
        // user scrolled up
        disableAutoScrollRef.current = true;
      }
    }
  }, []);
  useEvent('wheel', handleWheel, window, { passive: true }); // passive improves scrolling performance

  const placeholderText = useMemo(() => {
    return (chatSettings.mode as keyof typeof locale.chat.placeholder) in locale.chat.placeholder
      ? locale.chat.placeholder[chatSettings.mode as keyof typeof locale.chat.placeholder]
      : locale.chat.placeholder.default;
    // return task ? locale.chat.placeholder.withTask : locale.chat.placeholder.withoutTask;
  }, [chatSettings.mode]);

  // Create a stable reference for currentMode to prevent unnecessary re-renders
  const stableCurrentMode = useRef(currentMode);
  stableCurrentMode.current = currentMode;

  const itemContent = useCallback(
    (index: number, messageOrGroup: JoyCoderMessage | JoyCoderMessage[]) => {
      // browser session group
      if (Array.isArray(messageOrGroup)) {
        return (
          <BrowserSessionRow
            messages={messageOrGroup}
            isLast={index === groupedMessages.length - 1}
            lastModifiedMessage={modifiedMessages.at(-1)}
            onHeightChange={handleRowHeightChange}
            // Pass handlers for each message in the group
            isExpanded={(messageTs: number) => expandedRows[messageTs] ?? false}
            onToggleExpand={(messageTs: number) => {
              setExpandedRows((prev) => ({
                ...prev,
                [messageTs]: !prev[messageTs],
              }));
            }}
          />
        );
      }

      // regular message
      return (
        <ChatRow
          key={messageOrGroup.ts}
          message={messageOrGroup}
          isExpanded={expandedRows[messageOrGroup.ts] || false}
          onToggleExpand={() => toggleRowExpansion(messageOrGroup.ts)}
          lastModifiedMessage={modifiedMessages.at(-1)}
          isLast={index === groupedMessages.length - 1}
          onHeightChange={handleRowHeightChange}
          handleSecondaryButtonClick={handleSecondaryButtonClick}
          handlePrimaryButtonClick={handlePrimaryButtonClick}
          isStreaming={isStreaming}
          currentModeSlug={currentModeSlug}
          currentMode={stableCurrentMode.current}
          allMessages={groupedMessages}
          messageIndex={index}
          buttonInfo={{
            opacity:
              primaryButtonText || secondaryButtonText || isStreaming
                ? enableButtons || (isStreaming && !didClickCancel)
                  ? 1
                  : 0.5
                : 0,
            primaryDisabled: !enableButtons,
            secondaryDisabled: !enableButtons && !(isStreaming && !didClickCancel),
          }}
          onClose={() => startNewTask()}
        />
      );
    },
    [
      expandedRows,
      modifiedMessages,
      groupedMessages,
      handleRowHeightChange,
      handleSecondaryButtonClick,
      handlePrimaryButtonClick,
      isStreaming,
      primaryButtonText,
      secondaryButtonText,
      enableButtons,
      didClickCancel,
      toggleRowExpansion,
      currentModeSlug,
      // Removed currentMode from dependencies to prevent unnecessary re-renders
    ],
  );

  // Show loading state while login status is being checked
  if (isLogined === undefined) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
        }}
      >
        <span className="joycoder-auto-code-thinking-text">{locale.chat.thinking}</span>
      </div>
    );
  }

  return (
    <>
      {showMCP ? (
        <McpView
          onDone={() => {
            setShowMCP(false);
            showChatView();
          }}
          isRemoteEnvironment={isRemoteEnvironment}
        />
      ) : isLogined ? (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: isHidden ? 'none' : task ? 'flex' : 'initial',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          {task ? (
            <>
              {groupedMessages.length === 0 && (
                <span className="joycoder-auto-code-thinking-text">{locale.chat.thinking}</span>
              )}
            </>
          ) : (
            <Index
              showIndex={!task}
              showHistoryView={showHistoryView}
              showChatView={showChatView}
              setShowMcpView={setShowMcpView}
              isIDE={isIDE}
              isMacOs={isMacOs}
            />
          )}
          {/* {!task && (
            <AutoApproveMenu
              style={{
                marginBottom: -2,
                flex: '0 1 auto', // flex-grow: 0, flex-shrink: 1, flex-basis: auto
                minHeight: 0,
              }}
            />
          )} */}
          {postTaskInfo?.rootTask && postTaskInfo?.isParentTask && (
            <>
              <ParentTaskHeader taskLabel={postTaskInfo?.rootTask?.joyCoderMessage} />
            </>
          )}
          {task && (
            <div
              style={{
                position: 'absolute',
                height: '20px',
                width: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                overflow: 'hidden',
                right: '12px',
                top: postTaskInfo?.rootTask && postTaskInfo?.isParentTask ? '38px' : '8px',
                zIndex: 1
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.8';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1';
              }}
            >
              <div
                style={{
                  cursor: 'pointer',
                  height: '20px',
                  width: '20px',
                  borderRadius: '4px',
                  // backgroundColor: 'var(--vscode-editor-background)',
                  backgroundColor: 'var(--vscode-minimap-selectionOccurrenceHighlight)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'var(--vscode-button-secondaryForeground)',
                  zIndex: 1,
                }}
              >
                <i className="icon iconfont icon-guanbi" onClick={() => startNewTask()} />
              </div>
            </div>
          )}
          {task && (
            <>
              <div style={{ flexGrow: 1, display: 'flex' }} ref={scrollContainerRef}>
                <Virtuoso
                  ref={virtuosoRef}
                  key={task.ts}
                  className="scrollable"
                  style={{
                    flexGrow: 1,
                    overflowY: 'scroll',
                  }}
                  components={{
                    Footer: () => <div style={{ height: 5 }} />,
                  }}
                  increaseViewportBy={{ top: 3_000, bottom: Number.MAX_SAFE_INTEGER }}
                  data={groupedMessages}
                  itemContent={itemContent}
                  atBottomStateChange={(isAtBottom: any) => {
                    setIsAtBottom(isAtBottom);
                    if (isAtBottom) {
                      disableAutoScrollRef.current = false;
                    }
                    setShowScrollToBottom(disableAutoScrollRef.current && !isAtBottom);
                  }}
                  atBottomThreshold={10}
                  initialTopMostItemIndex={groupedMessages.length - 1}
                />
              </div>
              {/* <AutoApproveMenu /> */}
              {showScrollToBottom ? (
                <div
                  style={{
                    display: 'flex',
                    padding: '10px 20px 0px',
                  }}
                >
                  <ScrollToBottomButton
                    onClick={() => {
                      scrollToBottomSmooth();
                      disableAutoScrollRef.current = false;
                    }}
                  >
                    <span className="codicon codicon-chevron-down" style={{ fontSize: '18px' }}></span>
                  </ScrollToBottomButton>
                </div>
              ) : (
                <div
                  style={{
                    opacity:
                      primaryButtonText || secondaryButtonText || isStreaming
                        ? enableButtons || (isStreaming && !didClickCancel)
                          ? 1
                          : 0.5
                        : 0,
                    display: 'flex',
                    padding: '10px 20px 0px',
                  }}
                >
                  {primaryButtonText && !isStreaming && (
                    <VSCodeButton
                      disabled={!enableButtons}
                      style={{
                        flex: secondaryButtonText ? 1 : 2,
                        marginRight: secondaryButtonText ? '8px' : '0',
                        fontSize: '12px',
                        background: 'var(--vscode-button-background, #72747C)',
                        color: 'var(--vscode-button-foreground, #72747C)',
                        borderRadius: '4px',
                        height: '28px',
                      }}
                      onClick={() => handlePrimaryButtonClick(inputValue, selectedImages)}
                    >
                      {primaryButtonText}
                    </VSCodeButton>
                  )}
                  {(secondaryButtonText || isStreaming) && (
                    <VSCodeButton
                      appearance="secondary"
                      disabled={!enableButtons && !(isStreaming && !didClickCancel)}
                      style={{
                        flex: isStreaming ? 2 : 1,
                        marginLeft: isStreaming ? 0 : '8px',
                        color: 'var(--vscode-button-secondaryForeground, #72747C)',
                        fontSize: '12px',
                        background: 'var(--vscode-button-secondaryBackground, #72747C)',
                        borderRadius: '4px',
                        height: '28px',
                      }}
                      onClick={() => handleSecondaryButtonClick(inputValue, selectedImages)}
                    >
                      {isStreaming ? locale.common.cancel : secondaryButtonText}
                    </VSCodeButton>
                  )}
                </div>
              )}
            </>
          )}
          <ChatTextArea
            ref={textAreaRef}
            inputValue={inputValue}
            setInputValue={setInputValue}
            textAreaDisabled={textAreaDisabled}
            isStreaming={isStreaming}
            handleSecondaryButtonClick={handleSecondaryButtonClick}
            placeholderText={placeholderText}
            selectedImages={selectedImages}
            setSelectedImages={setSelectedImages}
            onSend={() => handleSendMessage(inputValue, selectedImages)}
            handleHistoryPrompt={handleHistoryPrompt}
            onSelectImages={selectImages}
            setShowPromptView={setShowPromptView}
            shouldDisableImages={shouldDisableImages}
            messages={messages}
            onHeightChange={() => {
              if (isAtBottom) {
                scrollToBottomAuto();
              }
            }}
          />
          {!task && <ChatHistoryView />}
        </div>
      ) : null}
      <Login isLogined={isLogined} />
    </>
  );
};

const ScrollToBottomButton = styled.div`
  background-color: color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 55%, transparent);
  border-radius: 3px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: 25px;

  &:hover {
    background-color: color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 90%, transparent);
  }

  &:active {
    background-color: color-mix(in srgb, var(--vscode-toolbar-hoverBackground) 70%, transparent);
  }
`;

export default ChatView;
